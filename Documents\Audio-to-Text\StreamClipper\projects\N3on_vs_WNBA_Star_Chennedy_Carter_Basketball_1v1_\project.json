{"id": "N3on_vs_WNBA_<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>_Basketball_1v1_", "name": "N3on vs WNBA Star Chen<PERSON><PERSON> Basketball 1v1!", "path": "C:\\Users\\<USER>\\Documents\\Audio-to-Text\\StreamClipper\\projects\\N3on_vs_WNBA_Star_Chen<PERSON>y_Carter_Basketball_1v1_", "created": "2025-04-22T10:38:25.464Z", "updated": "2025-04-30T04:11:57.313Z", "status": "analyzed", "hasVideo": true, "hasTranscription": true, "clipCount": 4, "videoPath": "C:\\Users\\<USER>\\Documents\\Audio-to-Text\\StreamClipper\\projects\\N3on_vs_WNBA_Star_Chen<PERSON>y_Carter_Basketball_1v1_\\original_video.mp4", "videoUrl": "https://www.youtube.com/watch?v=n7Ar0FbLj34", "transcriptionPath": "C:\\Users\\<USER>\\Documents\\Audio-to-Text\\StreamClipper\\projects\\N3on_vs_WNBA_<PERSON>_Chen<PERSON>y_Carter_Basketball_1v1_\\transcription.json", "hasClips": true, "clipsPath": "C:\\Users\\<USER>\\Documents\\Audio-to-Text\\StreamClipper\\projects\\N3on_vs_WNBA_<PERSON>_Chen<PERSON><PERSON>_Carter_Basketball_1v1_\\clips.json"}